# Doc Maker App

A multi-tenant SaaS application for dynamic report generation with DOCX to PDF conversion, built with TypeScript and Supabase.

## Features

- 🏢 **Multi-tenant Architecture**: Secure tenant isolation with Row Level Security (RLS)
- 📄 **Document Processing**: DOCX template processing with variable substitution
- 🔄 **PDF Conversion**: Automatic DOCX to PDF conversion
- 🚀 **Serverless Functions**: Supabase Edge Functions for document processing
- 🔐 **Authentication**: Built-in user authentication and authorization
- 💳 **Payment Integration**: Stripe integration for subscription management
- 📊 **Analytics**: Usage tracking and audit logging
- 🎨 **Template Management**: Dynamic document template system

## Tech Stack

- **Backend**: TypeScript, Node.js, Express
- **Database**: PostgreSQL with Supabase
- **Authentication**: Supabase Auth
- **Document Processing**: docxtemplater, pizzip, pdf-lib
- **Payment**: Stripe
- **Code Quality**: ESLint, Prettier
- **Testing**: Jest

## Prerequisites

- Node.js 18+ and npm 9+
- Supabase CLI
- Git

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Setup

Copy the environment template and configure your variables:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:
- Supabase URL and keys
- Database connection string
- Stripe keys
- SMTP settings
- Other service configurations

### 3. Initialize Supabase

```bash
# Initialize Supabase project (if not already done)
supabase init

# Start local Supabase services
npm run supabase:start

# Run database migrations
npm run db:migrate

# Generate TypeScript types
npm run supabase:generate-types
```

### 4. Development

```bash
# Start development server
npm run dev

# In another terminal, serve Supabase functions
npm run functions:serve
```

The application will be available at:
- API: http://localhost:3000
- Supabase Studio: http://localhost:54323
- Supabase API: http://localhost:54321

## Project Structure

```
doc-maker-app/
├── src/                          # Application source code
│   ├── controllers/              # Route controllers
│   ├── middleware/               # Express middleware
│   ├── models/                   # Data models
│   ├── routes/                   # API routes
│   ├── services/                 # Business logic
│   ├── types/                    # TypeScript type definitions
│   ├── utils/                    # Utility functions
│   └── index.ts                  # Application entry point
├── supabase/                     # Supabase configuration
│   ├── functions/                # Edge Functions
│   │   └── document-processor/   # Document processing function
│   ├── migrations/               # Database migrations
│   └── config.toml               # Supabase configuration
├── tests/                        # Test files
├── templates/                    # Document templates
├── uploads/                      # File uploads (gitignored)
└── generated/                    # Generated documents (gitignored)
```

## Available Scripts

### Development
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server

### Code Quality
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting
- `npm run type-check` - Run TypeScript type checking

### Testing
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

### Supabase
- `npm run supabase:start` - Start local Supabase
- `npm run supabase:stop` - Stop local Supabase
- `npm run supabase:reset` - Reset local database
- `npm run db:migrate` - Run database migrations
- `npm run functions:serve` - Serve Edge Functions locally
- `npm run functions:deploy` - Deploy Edge Functions

## Database Schema

The application uses a multi-tenant database schema with the following main tables:

- **tenants**: Organization/company information
- **user_profiles**: User information linked to Supabase auth
- **document_templates**: DOCX templates with variable definitions
- **documents**: Generated documents with processing status
- **audit_logs**: Activity tracking and audit trail
- **storage_usage**: Storage usage tracking per tenant

## Multi-Tenant Security

The application implements Row Level Security (RLS) to ensure data isolation:

- Users can only access data within their tenant
- All database queries are automatically filtered by tenant
- API endpoints validate tenant membership
- File storage is organized by tenant

## Document Processing Flow

1. User uploads a DOCX template
2. Template variables are extracted and stored
3. User provides input data for document generation
4. Edge Function processes the template with input data
5. Generated DOCX is converted to PDF
6. Files are stored and linked to the document record
7. User can download the generated documents

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/logout` - User logout

### Templates
- `GET /api/v1/templates` - List templates
- `POST /api/v1/templates` - Create template
- `GET /api/v1/templates/:id` - Get template
- `PUT /api/v1/templates/:id` - Update template
- `DELETE /api/v1/templates/:id` - Delete template

### Documents
- `GET /api/v1/documents` - List documents
- `POST /api/v1/documents` - Create document
- `GET /api/v1/documents/:id` - Get document
- `POST /api/v1/documents/:id/process` - Process document

## Deployment

### Production Environment

1. Set up production Supabase project
2. Configure environment variables
3. Deploy Edge Functions: `npm run functions:deploy`
4. Run migrations on production database
5. Deploy application to your hosting platform

### Environment Variables

Ensure all required environment variables are set in production:
- Database connections
- API keys (Supabase, Stripe, etc.)
- File storage configuration
- Email service settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

MIT License - see LICENSE file for details
