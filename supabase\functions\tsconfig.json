{
  "compilerOptions": {
    /* Language and Environment */
    "target": "ES2022",
    "lib": ["ES2022", "WebWorker"],
    "module": "ESNext",
    "moduleResolution": "node",
    "allowJs": true,
    "checkJs": false,
    
    /* Deno/Edge Functions specific */
    "moduleDetection": "force",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    
    /* Type Checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    
    /* Interop Constraints */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    
    /* Completeness */
    "skipLibCheck": true,
    
    /* Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/shared/*": ["../shared/*"]
    },
    
    /* Deno types */
    "types": ["https://deno.land/x/xhr@0.3.0/mod.d.ts"]
  },
  "include": [
    "./**/*"
  ],
  "exclude": [
    "node_modules"
  ]
}
