import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface DocumentProcessRequest {
  documentId: string;
  templateId: string;
  inputData: Record<string, any>;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Parse request body
    const { documentId, templateId, inputData }: DocumentProcessRequest = await req.json();

    console.log(`Processing document ${documentId} with template ${templateId}`);

    // Update document status to processing
    await supabaseClient
      .from('documents')
      .update({
        status: 'processing',
        processing_started_at: new Date().toISOString(),
      })
      .eq('id', documentId);

    // Get template information
    const { data: template, error: templateError } = await supabaseClient
      .from('document_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (templateError || !template) {
      throw new Error(`Template not found: ${templateError?.message}`);
    }

    // Simulate document processing (replace with actual DOCX processing logic)
    console.log('Processing template:', template.name);
    console.log('Input data:', inputData);

    // Here you would implement the actual document processing logic:
    // 1. Load the DOCX template from storage
    // 2. Replace variables with input data using docxtemplater
    // 3. Generate the processed DOCX file
    // 4. Convert to PDF if needed
    // 5. Upload the generated files to storage
    // 6. Update the document record with file paths

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock file paths (replace with actual file paths after processing)
    const generatedFilePath = `documents/${documentId}/generated.docx`;
    const pdfFilePath = `documents/${documentId}/generated.pdf`;

    // Update document status to completed
    const { error: updateError } = await supabaseClient
      .from('documents')
      .update({
        status: 'completed',
        generated_file_path: generatedFilePath,
        pdf_file_path: pdfFilePath,
        processing_completed_at: new Date().toISOString(),
        file_size_bytes: 1024 * 50, // Mock file size
      })
      .eq('id', documentId);

    if (updateError) {
      throw new Error(`Failed to update document: ${updateError.message}`);
    }

    // Log the processing completion
    await supabaseClient
      .from('audit_logs')
      .insert({
        action: 'document_processed',
        resource_type: 'document',
        resource_id: documentId,
        new_values: {
          status: 'completed',
          generated_file_path: generatedFilePath,
          pdf_file_path: pdfFilePath,
        },
      });

    return new Response(
      JSON.stringify({
        success: true,
        documentId,
        generatedFilePath,
        pdfFilePath,
        message: 'Document processed successfully',
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error processing document:', error);

    // If we have a documentId, update the document status to failed
    try {
      const { documentId } = await req.json();
      if (documentId) {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_ANON_KEY') ?? '',
          {
            global: {
              headers: { Authorization: req.headers.get('Authorization')! },
            },
          }
        );

        await supabaseClient
          .from('documents')
          .update({
            status: 'failed',
            error_message: error.message,
            processing_completed_at: new Date().toISOString(),
          })
          .eq('id', documentId);
      }
    } catch (updateError) {
      console.error('Failed to update document status to failed:', updateError);
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
