{"name": "doc-maker-app", "version": "1.0.0", "description": "Multi-tenant SaaS application for dynamic report generation with DOCX to PDF conversion", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:generate-types": "supabase gen types typescript --local > src/types/supabase.ts", "db:migrate": "supabase migration up", "db:seed": "supabase seed", "functions:serve": "supabase functions serve", "functions:deploy": "supabase functions deploy"}, "keywords": ["supabase", "typescript", "saas", "multi-tenant", "document-processing", "pdf-conversion", "docx", "reports"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "docxtemplater": "^3.44.0", "pizzip": "^3.1.6", "xlsx": "^0.18.5", "stripe": "^14.12.0", "axios": "^1.6.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "pdf-lib": "^1.17.1", "puppeteer": "^21.6.1", "nodemailer": "^6.9.7", "winston": "^3.11.0", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.1", "typescript": "^5.3.3", "tsx": "^4.6.2", "tsc-alias": "^1.8.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}